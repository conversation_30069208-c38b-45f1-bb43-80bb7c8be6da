# BRAIN Alpha Automation System

A comprehensive Python automation system for creating, submitting, and analyzing alpha strategies using the WorldQuant BRAIN API.

## Features

### 🚀 **Fully Automated Alpha Pipeline**
- Automatic authentication (including biometric support)
- Programmatic alpha expression generation
- Automated submission and monitoring
- Complete results retrieval and analysis

### 📊 **Advanced Alpha Generation**
- **Momentum Strategies**: Various lookback periods and variations
- **Mean Reversion**: Different window sizes and formulations  
- **Correlation-Based**: Price-volume relationships
- **Parameter Sweeping**: Systematic testing of parameter ranges
- **Custom Templates**: Flexible expression generation

### 🔍 **Performance Analysis**
- Automatic Sharpe ratio calculation
- PnL and turnover analysis
- Fitness score tracking
- Top performer filtering
- CSV export for further analysis

### ⚡ **Batch Processing**
- Process multiple alphas simultaneously
- Intelligent batching to avoid API limits
- Comprehensive error handling and retry logic
- Progress tracking and status updates

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Setup Credentials
Your credentials are already configured in `.brain_credentials`:
```json
["<EMAIL>", "Saj$31434"]
```

### 3. Run Basic Automation
```bash
# Interactive mode with menu
python ALPHA.py

# Quick automation with specific strategy
python run_alpha_automation.py momentum 10
python run_alpha_automation.py comprehensive 15
python run_alpha_automation.py custom
```

## Usage Examples

### Basic Pipeline
```python
from ALPHA import BrainAlphaAutomator

automator = BrainAlphaAutomator()
results = automator.automated_alpha_pipeline()
```

### Advanced Batch Processing
```python
# Momentum strategy sweep
results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=10)

# Custom expressions
custom_exprs = [
    "rank(close / ts_mean(close, 20))",
    "correlation(close, volume, 15)",
    "-(close - ts_mean(close, 15)) / ts_std(close, 15)"
]
results = automator.automated_alpha_pipeline(custom_expressions=custom_exprs)
```

### Parameter Sweeping
```python
# Test correlation with different windows
template = "correlation(close, volume, {window})"
param_ranges = {"window": [10, 15, 20, 25, 30]}
expressions = automator.generate_parameter_sweep_expressions(template, param_ranges)
results = automator.automated_alpha_pipeline(custom_expressions=expressions)
```

## Available Strategies

### 1. **Momentum Sweep** (`momentum_sweep`)
Tests momentum-based strategies with different lookback periods:
- `close / delay(close, N) - 1`
- `rank(close / delay(close, N))`
- `ts_rank(close / delay(close, N), 20)`

### 2. **Mean Reversion Sweep** (`mean_reversion_sweep`)
Tests mean reversion strategies with various windows:
- `-(close - ts_mean(close, N)) / ts_std(close, N)`
- `rank(ts_mean(close, N) - close)`
- `(ts_mean(close, N) - close) / close`

### 3. **Correlation Sweep** (`correlation_sweep`)
Tests correlation-based strategies:
- `correlation(close, volume, N)`
- `correlation(close/delay(close,1)-1, volume, N)`
- `rank(correlation(close, volume, N))`

### 4. **Comprehensive** (`comprehensive`)
Combines all strategy types for broad testing.

## Command Line Interface

```bash
# Run specific strategy with batch size
python run_alpha_automation.py [strategy] [batch_size]

# Examples:
python run_alpha_automation.py momentum 8
python run_alpha_automation.py mean_reversion 12
python run_alpha_automation.py correlation 6
python run_alpha_automation.py comprehensive 15
python run_alpha_automation.py custom
```

## Configuration

Edit `alpha_config.json` to customize:
- Default simulation settings
- Strategy parameters
- Performance filters
- Custom expression templates

## Output Files

The system automatically generates:
- `alpha_results_YYYYMMDD_HHMMSS.csv` - Detailed results
- Console output with top performers
- Performance analysis summaries

## Advanced Features

### Multiple Alpha Submission
```python
# Submit 2-10 alphas simultaneously (requires MULTI_SIMULATION permission)
expressions = ["expr1", "expr2", "expr3"]
multi_url = automator.submit_multiple_alphas(expressions)
```

### Performance Filtering
```python
# Filter top performers
top_alphas = automator.filter_top_performers(results, min_sharpe=0.5, top_n=5)
```

### Results Analysis
```python
# Detailed performance analysis
performance_summary = automator.analyze_alpha_performance(results)
csv_file = automator.save_results_to_csv(results)
```

## Expression Types Available

- `simple`: Basic close price
- `momentum`: Price momentum strategies
- `mean_reversion`: Mean reversion strategies
- `volume_price`: Price-volume correlations
- `rsi_based`: RSI-style indicators
- `bollinger`: Bollinger band style
- `macd`: MACD-style indicators
- `volume_momentum`: Volume-based momentum
- `price_volume_trend`: Combined price-volume trends
- `volatility`: Volatility measures
- `trend_strength`: Trend strength indicators

## Error Handling

The system includes comprehensive error handling for:
- Authentication failures (including biometric prompts)
- API rate limits and timeouts
- Simulation failures
- Network connectivity issues
- Invalid expressions

## Tips for Success

1. **Start Small**: Begin with 3-5 alphas to test the system
2. **Monitor Performance**: Use Sharpe ratio > 0.3 as a baseline filter
3. **Diversify Strategies**: Mix momentum, mean reversion, and correlation approaches
4. **Parameter Sweeping**: Systematically test parameter ranges
5. **Save Results**: Always export to CSV for further analysis

## Troubleshooting

- **Authentication Issues**: Check credentials in `.brain_credentials`
- **API Limits**: Reduce batch sizes or add delays between submissions
- **Biometric Auth**: Follow the URL prompt for biometric authentication
- **Simulation Failures**: Check expression syntax and parameter ranges

---

**Ready to automate your alpha research!** 🚀

Start with: `python ALPHA.py` for interactive mode or `python run_alpha_automation.py comprehensive 10` for immediate automation.
