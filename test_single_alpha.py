#!/usr/bin/env python3
"""
Test script to submit and monitor a single alpha
This helps verify the URL handling fix
"""

from ALPHA import BrainAlphaAutomator

def test_single_alpha():
    """Test submitting a single alpha to verify the fix"""
    print("🧪 Testing Single Alpha Submission")
    print("=" * 50)
    
    try:
        # Initialize automator
        automator = BrainAlphaAutomator()
        
        # Authenticate
        print("🔐 Authenticating...")
        auth_result = automator.authenticate()
        print("✅ Authentication successful!")
        
        # Test with a simple momentum expression
        test_expression = "close / delay(close, 10) - 1"
        print(f"📊 Testing expression: {test_expression}")
        
        # Submit alpha
        print("🚀 Submitting alpha...")
        simulation_url = automator.submit_alpha(test_expression)
        print(f"✅ Alpha submitted successfully!")
        print(f"📍 Tracking URL: {simulation_url}")
        
        # Wait for completion
        print("⏳ Waiting for simulation to complete...")
        alpha_id = automator.wait_for_simulation(simulation_url)
        print(f"🎉 Simulation completed! Alpha ID: {alpha_id}")
        
        # Get results
        print("📈 Retrieving results...")
        results = automator.get_alpha_results(alpha_id)
        
        # Show summary
        print("\n📊 Results Summary:")
        print(f"Expression: {test_expression}")
        print(f"Alpha ID: {alpha_id}")
        
        if 'sharpe' in results['performance_data']:
            sharpe_data = results['performance_data']['sharpe']
            if 'records' in sharpe_data and sharpe_data['records']:
                sharpe_ratio = sharpe_data['records'][-1].get('value', 'N/A')
                print(f"Sharpe Ratio: {sharpe_ratio}")
        
        fitness = results['alpha_details'].get('fitness', 'N/A')
        print(f"Fitness: {fitness}")
        
        print("\n✅ Single alpha test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_single_alpha()
    if success:
        print("\n🎉 System is ready for batch automation!")
        print("Run: python run_alpha_automation.py comprehensive 5")
    else:
        print("\n🔧 Please check the error and try again.")
