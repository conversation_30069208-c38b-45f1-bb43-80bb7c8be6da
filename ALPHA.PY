import requests
import json
import time
import random
import itertools
import pandas as pd
import pickle
import os
from datetime import datetime, timedelta
from os.path import expanduser
from urllib.parse import urljoin

class BrainAlphaAutomator:
    def __init__(self, credentials_file='.brain_credentials'):
        self.session = requests.Session()
        self.base_url = 'https://api.worldquantbrain.com'
        self.session_file = '.brain_session.pkl'
        self.login_count_file = '.brain_login_count.json'
        self.authenticated = False
        self.load_credentials(credentials_file)
        self.load_session()
    
    def load_credentials(self, credentials_file):
        """Load credentials from JSON file"""
        try:
            # Try current directory first, then home directory
            if not credentials_file.startswith('.') and not credentials_file.startswith('/'):
                credentials_file = f"./{credentials_file}"

            try:
                with open(credentials_file, 'r') as f:
                    self.session.auth = tuple(json.load(f))
                print(f"Credentials loaded from {credentials_file}")
            except FileNotFoundError:
                # Fallback to home directory
                home_path = expanduser(f"~/{credentials_file}")
                with open(home_path, 'r') as f:
                    self.session.auth = tuple(json.load(f))
                print(f"Credentials loaded from {home_path}")

        except FileNotFoundError:
            print(f"Credentials file {credentials_file} not found. Please create it with format: ['email', 'password']")

    def load_session(self):
        """Load existing session to avoid re-authentication"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'rb') as f:
                    session_data = pickle.load(f)

                # Check if session is still valid (less than 23 hours old)
                if datetime.now() - session_data['timestamp'] < timedelta(hours=23):
                    self.session.cookies.update(session_data['cookies'])
                    self.authenticated = True
                    print("✅ Loaded existing session (avoiding new login)")
                    return True
                else:
                    print("⚠️ Existing session expired")
                    os.remove(self.session_file)
        except Exception as e:
            print(f"Could not load session: {e}")

        return False

    def save_session(self):
        """Save current session for reuse"""
        try:
            session_data = {
                'cookies': dict(self.session.cookies),
                'timestamp': datetime.now()
            }
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
            print("💾 Session saved for reuse")
        except Exception as e:
            print(f"Could not save session: {e}")

    def check_login_limit(self):
        """Check and update daily login count"""
        today = datetime.now().strftime('%Y-%m-%d')

        try:
            if os.path.exists(self.login_count_file):
                with open(self.login_count_file, 'r') as f:
                    login_data = json.load(f)
            else:
                login_data = {}

            # Reset count if it's a new day
            if login_data.get('date') != today:
                login_data = {'date': today, 'count': 0}

            current_count = login_data.get('count', 0)

            if current_count >= 20:  # Conservative limit (5 below max)
                raise Exception(f"⚠️ DAILY LOGIN LIMIT REACHED! ({current_count}/25 logins used today). Please wait until tomorrow or use existing session.")

            # Increment count
            login_data['count'] = current_count + 1

            with open(self.login_count_file, 'w') as f:
                json.dump(login_data, f)

            print(f"📊 Login count: {login_data['count']}/25 for today")

            if login_data['count'] >= 15:
                print("⚠️ WARNING: Approaching daily login limit! Consider using session persistence.")

        except Exception as e:
            if "DAILY LOGIN LIMIT" in str(e):
                raise e
            print(f"Could not check login limit: {e}")
    
    def authenticate(self, force_new_login=False):
        """Authenticate with the BRAIN API with session persistence"""

        # Check if we already have a valid session
        if self.authenticated and not force_new_login:
            print("✅ Already authenticated with existing session")
            return True

        # Check daily login limit before attempting new login
        if not force_new_login:
            self.check_login_limit()

        print("🔐 Attempting authentication...")
        response = self.session.post(f'{self.base_url}/authentication')

        if response.status_code == 401:
            if response.headers.get("WWW-Authenticate") == "persona":
                # Handle biometric authentication
                auth_url = urljoin(response.url, response.headers["Location"])
                print(f"🔒 Biometric authentication required")
                print(f"🌐 Please complete authentication at: {auth_url}")
                input("Press Enter after completing biometric authentication...")

                # Complete the biometric flow
                biometric_response = self.session.post(urljoin(response.url, response.headers["Location"]))
                print("✅ Biometric authentication completed")

                # Check if biometric auth was successful
                if biometric_response.status_code == 201:
                    print("✅ Authentication successful!")
                    self.authenticated = True
                    self.save_session()  # Save session for reuse
                    return biometric_response.json()
                else:
                    raise Exception(f"❌ Biometric authentication failed with status code: {biometric_response.status_code}")
            else:
                raise Exception("❌ Authentication failed: Incorrect email and password")
        elif response.status_code == 201:
            print("✅ Authentication successful!")
            self.authenticated = True
            self.save_session()  # Save session for reuse
            return response.json()
        else:
            raise Exception(f"❌ Authentication failed with status code: {response.status_code}")

    def ensure_authenticated(self):
        """Ensure we have a valid authentication before making API calls"""
        if not self.authenticated:
            return self.authenticate()
        return True
    
    def create_alpha_expression(self, expression_type="simple"):
        """Generate alpha expressions using only verified working BRAIN operators"""
        expressions = {
            "simple": "close",
            "momentum": "rank(close / ts_mean(close, 5))",
            "mean_reversion": "-(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "volume_price": "correlation(close, volume, 10)",
            "rsi_based": "rank(close) - 0.5",
            "bollinger": "(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "macd": "ts_mean(close, 12) - ts_mean(close, 26)",
            "volume_momentum": "correlation(close, volume, 20)",
            "price_volume_trend": "rank(close) * rank(volume)",
            "volatility": "ts_std(close, 20)",
            "trend_strength": "correlation(close, ts_rank(close, 20), 10)",
            "simple_momentum": "rank(close)",
            "price_rank": "rank(close)",
            "volume_rank": "rank(volume)",
            "simple_return": "close / ts_mean(close, 2) - 1"
        }
        return expressions.get(expression_type, expressions["simple"])

    def generate_parameter_sweep_expressions(self, base_expression_template, parameter_ranges):
        """Generate multiple expressions by sweeping parameters"""
        expressions = []

        # Create all combinations of parameters
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())

        for combination in itertools.product(*param_values):
            params = dict(zip(param_names, combination))
            expression = base_expression_template.format(**params)
            expressions.append(expression)

        return expressions

    def generate_momentum_variations(self, lookback_range=(5, 30), count=10):
        """Generate momentum alpha variations using verified operators"""
        expressions = []
        lookbacks = random.sample(range(lookback_range[0], lookback_range[1] + 1), min(count, lookback_range[1] - lookback_range[0] + 1))

        for lookback in lookbacks:
            expressions.append(f"rank(close / ts_mean(close, {lookback}))")
            expressions.append(f"close / ts_mean(close, {lookback}) - 1")
            expressions.append(f"ts_rank(close, {lookback})")

        return expressions

    def generate_mean_reversion_variations(self, window_range=(10, 50), count=10):
        """Generate mean reversion alpha variations"""
        expressions = []
        windows = random.sample(range(window_range[0], window_range[1] + 1), min(count, window_range[1] - window_range[0] + 1))

        for window in windows:
            expressions.append(f"-(close - ts_mean(close, {window})) / ts_std(close, {window})")
            expressions.append(f"rank(ts_mean(close, {window}) - close)")
            expressions.append(f"(ts_mean(close, {window}) - close) / close")

        return expressions

    def generate_correlation_variations(self, window_range=(10, 30), count=10):
        """Generate correlation-based alpha variations"""
        expressions = []
        windows = random.sample(range(window_range[0], window_range[1] + 1), min(count, window_range[1] - window_range[0] + 1))

        for window in windows:
            expressions.append(f"correlation(close, volume, {window})")
            expressions.append(f"correlation(close, volume, {window})")
            expressions.append(f"rank(correlation(close, volume, {window}))")

        return expressions
    
    def submit_alpha(self, alpha_expression, settings=None):
        """Submit an alpha for simulation"""
        if settings is None:
            # Default simulation settings
            settings = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'universe': 'TOP3000',
                'delay': 1,
                'decay': 15,
                'neutralization': 'SUBINDUSTRY',
                'truncation': 0.08,
                'pasteurization': 'ON',
                'testPeriod': 'P1Y6M',
                'unitHandling': 'VERIFY',
                'nanHandling': 'OFF',
                'language': 'FASTEXPR',
                'visualization': False,
            }
        
        simulation_data = {
            'type': 'REGULAR',
            'settings': settings,
            'regular': alpha_expression
        }
        
        print(f"Submitting alpha: {alpha_expression}")
        response = self.session.post(f'{self.base_url}/simulations', json=simulation_data)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"Alpha submitted successfully. Tracking at: {simulation_url}")

            # Ensure we have the correct URL format
            if not simulation_url.startswith('http'):
                simulation_url = f"{self.base_url}{simulation_url}"

            return simulation_url
        else:
            raise Exception(f"Alpha submission failed: {response.status_code} - {response.text}")
    
    def wait_for_simulation(self, simulation_url):
        """Wait for simulation to complete and return alpha ID"""
        print("Waiting for simulation to complete...")

        # Fix URL construction - simulation_url should be the full URL
        if simulation_url.startswith('http'):
            full_url = simulation_url
        else:
            full_url = f'{self.base_url}{simulation_url}'

        print(f"Monitoring simulation at: {full_url}")

        while True:
            try:
                response = self.session.get(full_url)

                if response.status_code != 200:
                    print(f"Warning: Received status code {response.status_code}")
                    time.sleep(10)
                    continue

                data = response.json()

                if response.headers.get("Retry-After"):
                    retry_after = float(response.headers["Retry-After"])
                    retry_after_int = int(retry_after) if retry_after == int(retry_after) else int(retry_after) + 1
                    print(f"Simulation in progress. Sleeping for {retry_after_int} seconds...")
                    time.sleep(retry_after_int)
                else:
                    if data.get('status') == 'COMPLETE':
                        alpha_id = data.get('alpha')
                        print(f"Simulation completed! Alpha ID: {alpha_id}")
                        return alpha_id
                    elif data.get('status') in ['ERROR', 'FAIL', 'TIMEOUT']:
                        raise Exception(f"Simulation failed with status: {data.get('status')} - {data.get('message', 'No error message')}")
                    else:
                        print(f"Simulation status: {data.get('status')}")
                        time.sleep(10)  # Wait 10 seconds before checking again

            except Exception as e:
                print(f"Error checking simulation status: {e}")
                print("Retrying in 15 seconds...")
                time.sleep(15)
    
    def get_alpha_results(self, alpha_id):
        """Retrieve alpha simulation results"""
        print(f"Retrieving results for alpha: {alpha_id}")
        
        # Get alpha details
        alpha_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}')

        if alpha_response.status_code != 200:
            print(f"Warning: Could not get alpha details (status: {alpha_response.status_code})")
            alpha_data = {}
        else:
            try:
                alpha_data = alpha_response.json()
            except json.JSONDecodeError:
                print("Warning: Could not parse alpha details JSON")
                alpha_data = {}

        # Get available recordsets
        recordsets_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets')

        if recordsets_response.status_code != 200:
            print(f"Warning: Could not get recordsets (status: {recordsets_response.status_code})")
            recordsets = []
        else:
            try:
                recordsets = recordsets_response.json()
            except json.JSONDecodeError:
                print("Warning: Could not parse recordsets JSON")
                recordsets = []
        
        results = {
            'alpha_details': alpha_data,
            'available_recordsets': recordsets,
            'performance_data': {}
        }
        
        # Get key performance metrics
        key_metrics = ['pnl', 'sharpe', 'turnover', 'yearly-stats']
        
        for metric in key_metrics:
            try:
                # Wait for recordset to be ready
                while True:
                    metric_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets/{metric}')
                    if metric_response.headers.get("Retry-After"):
                        retry_after = float(metric_response.headers["Retry-After"])
                        retry_after_int = int(retry_after) if retry_after == int(retry_after) else int(retry_after) + 1
                        print(f"Waiting for {metric} data. Sleeping for {retry_after_int} seconds...")
                        time.sleep(retry_after_int)
                    else:
                        if metric_response.status_code == 200:
                            try:
                                results['performance_data'][metric] = metric_response.json()
                                print(f"Retrieved {metric} data")
                            except json.JSONDecodeError:
                                print(f"Warning: Could not parse {metric} data")
                                results['performance_data'][metric] = {}
                        else:
                            print(f"Warning: Could not get {metric} data (status: {metric_response.status_code})")
                            results['performance_data'][metric] = {}
                        break
            except Exception as e:
                print(f"Could not retrieve {metric}: {e}")
        
        return results

    def analyze_alpha_performance(self, results):
        """Analyze and rank alpha performance"""
        performance_summary = []

        for result in results:
            summary = {
                'expression': result['expression'],
                'alpha_id': result['alpha_details'].get('id', 'N/A'),
                'sharpe_ratio': None,
                'total_pnl': None,
                'turnover': None,
                'fitness': None
            }

            # Extract Sharpe ratio
            if 'sharpe' in result['performance_data']:
                sharpe_data = result['performance_data']['sharpe']
                if 'records' in sharpe_data and sharpe_data['records']:
                    summary['sharpe_ratio'] = sharpe_data['records'][-1].get('value', None)

            # Extract total PnL
            if 'pnl' in result['performance_data']:
                pnl_data = result['performance_data']['pnl']
                if 'records' in pnl_data and pnl_data['records']:
                    summary['total_pnl'] = sum(record.get('value', 0) for record in pnl_data['records'])

            # Extract turnover
            if 'turnover' in result['performance_data']:
                turnover_data = result['performance_data']['turnover']
                if 'records' in turnover_data and turnover_data['records']:
                    summary['turnover'] = sum(record.get('value', 0) for record in turnover_data['records']) / len(turnover_data['records'])

            # Extract fitness score
            alpha_details = result['alpha_details']
            summary['fitness'] = alpha_details.get('fitness', None)

            performance_summary.append(summary)

        # Sort by Sharpe ratio (descending)
        performance_summary.sort(key=lambda x: x['sharpe_ratio'] or -999, reverse=True)

        return performance_summary

    def save_results_to_csv(self, results, filename=None):
        """Save alpha results to CSV file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"alpha_results_{timestamp}.csv"

        performance_data = self.analyze_alpha_performance(results)
        df = pd.DataFrame(performance_data)
        df.to_csv(filename, index=False)
        print(f"Results saved to {filename}")
        return filename

    def filter_top_performers(self, results, min_sharpe=0.5, top_n=10):
        """Filter and return top performing alphas"""
        performance_data = self.analyze_alpha_performance(results)

        # Filter by minimum Sharpe ratio
        filtered = [alpha for alpha in performance_data if alpha['sharpe_ratio'] and alpha['sharpe_ratio'] >= min_sharpe]

        # Return top N
        return filtered[:top_n]

    def submit_multiple_alphas(self, expressions_list, settings=None):
        """Submit multiple alphas simultaneously"""
        if len(expressions_list) < 2 or len(expressions_list) > 10:
            raise ValueError("Multiple simulations require 2-10 expressions")
        
        if settings is None:
            settings = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'universe': 'TOP3000',
                'delay': 1,
                'decay': 15,
                'neutralization': 'SUBINDUSTRY',
                'truncation': 0.08,
                'pasteurization': 'ON',
                'testPeriod': 'P1Y6M',
                'unitHandling': 'VERIFY',
                'nanHandling': 'OFF',
                'language': 'FASTEXPR',
                'visualization': False,
            }
        
        simulations = []
        for expr in expressions_list:
            simulations.append({
                'type': 'REGULAR',
                'settings': settings,
                'regular': expr
            })
        
        response = self.session.post(f'{self.base_url}/simulations', json=simulations)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"Multiple alphas submitted successfully. Tracking at: {simulation_url}")
            return simulation_url
        else:
            raise Exception(f"Multiple alpha submission failed: {response.status_code} - {response.text}")
    
    def automated_alpha_pipeline(self, expression_types=None, custom_expressions=None):
        """Complete automated pipeline: authenticate, create, submit, and retrieve results"""
        try:
            # Step 1: Ensure authentication (reuse session if possible)
            self.ensure_authenticated()
            
            # Step 2: Create alpha expressions
            expressions = []
            if custom_expressions:
                expressions = custom_expressions
            else:
                if expression_types is None:
                    expression_types = ["simple", "momentum", "mean_reversion"]
                
                for expr_type in expression_types:
                    expressions.append(self.create_alpha_expression(expr_type))
            
            results = []
            
            # Step 3: Submit and process each alpha
            for i, expression in enumerate(expressions):
                print(f"\n--- Processing Alpha {i+1}/{len(expressions)} ---")
                
                # Submit alpha
                simulation_url = self.submit_alpha(expression)
                
                # Wait for completion
                alpha_id = self.wait_for_simulation(simulation_url)
                
                # Get results
                alpha_results = self.get_alpha_results(alpha_id)
                alpha_results['expression'] = expression
                results.append(alpha_results)
                
                print(f"Alpha {i+1} completed successfully!")
            
            return results
            
        except Exception as e:
            print(f"Pipeline failed: {e}")
            return None

    def batch_alpha_generation_pipeline(self, strategy="comprehensive", batch_size=5):
        """Advanced pipeline for generating and testing multiple alpha strategies"""
        try:
            # Step 1: Ensure authentication (reuse session if possible)
            self.ensure_authenticated()

            all_expressions = []

            if strategy == "momentum_sweep":
                all_expressions = self.generate_momentum_variations(count=batch_size)
            elif strategy == "mean_reversion_sweep":
                all_expressions = self.generate_mean_reversion_variations(count=batch_size)
            elif strategy == "correlation_sweep":
                all_expressions = self.generate_correlation_variations(count=batch_size)
            elif strategy == "parameter_sweep":
                # Example parameter sweep for momentum strategies
                template = "close / delay(close, {lookback}) - 1"
                param_ranges = {"lookback": range(5, 21)}
                all_expressions = self.generate_parameter_sweep_expressions(template, param_ranges)[:batch_size]
            elif strategy == "comprehensive":
                # Mix of different strategies
                momentum_exprs = self.generate_momentum_variations(count=batch_size//3)
                mean_rev_exprs = self.generate_mean_reversion_variations(count=batch_size//3)
                corr_exprs = self.generate_correlation_variations(count=batch_size//3)
                all_expressions = momentum_exprs + mean_rev_exprs + corr_exprs

            print(f"Generated {len(all_expressions)} expressions for testing")

            # Process in batches to avoid overwhelming the API
            all_results = []
            batch_size_actual = min(batch_size, len(all_expressions))

            for i in range(0, len(all_expressions), batch_size_actual):
                batch = all_expressions[i:i + batch_size_actual]
                print(f"\n=== Processing Batch {i//batch_size_actual + 1} ===")

                batch_results = self.automated_alpha_pipeline(custom_expressions=batch)
                if batch_results:
                    all_results.extend(batch_results)

                # Small delay between batches
                if i + batch_size_actual < len(all_expressions):
                    print("Waiting 30 seconds before next batch...")
                    time.sleep(30)

            # Analyze results
            if all_results:
                print(f"\n=== Analysis of {len(all_results)} Alphas ===")
                performance_summary = self.analyze_alpha_performance(all_results)

                # Save results
                csv_file = self.save_results_to_csv(all_results)

                # Show top performers
                top_performers = self.filter_top_performers(all_results, min_sharpe=0.3, top_n=5)

                print(f"\nTop {len(top_performers)} Performers (Sharpe > 0.3):")
                for i, alpha in enumerate(top_performers, 1):
                    print(f"{i}. Expression: {alpha['expression']}")
                    print(f"   Sharpe Ratio: {alpha['sharpe_ratio']:.4f}")
                    print(f"   Fitness: {alpha['fitness']}")
                    print(f"   Alpha ID: {alpha['alpha_id']}")
                    print()

                return all_results, performance_summary, top_performers

            return None, None, None

        except Exception as e:
            print(f"Batch pipeline failed: {e}")
            return None, None, None

# Example usage and advanced automation demos
def main():
    # Initialize the automator
    automator = BrainAlphaAutomator()

    print("=== BRAIN Alpha Automation System ===")
    print("Choose an automation strategy:")
    print("1. Basic Pipeline (3 default expressions)")
    print("2. Momentum Strategy Sweep")
    print("3. Mean Reversion Strategy Sweep")
    print("4. Correlation Strategy Sweep")
    print("5. Comprehensive Multi-Strategy")
    print("6. Custom Expression List")
    print("7. Parameter Sweep Example")

    choice = input("Enter choice (1-7) or press Enter for default: ").strip()

    if choice == "2":
        print("\n=== Momentum Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=8)

    elif choice == "3":
        print("\n=== Mean Reversion Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("mean_reversion_sweep", batch_size=8)

    elif choice == "4":
        print("\n=== Correlation Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("correlation_sweep", batch_size=8)

    elif choice == "5":
        print("\n=== Comprehensive Multi-Strategy ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("comprehensive", batch_size=12)

    elif choice == "6":
        print("\n=== Custom Expression List ===")
        custom_expressions = [
            "rank(close / ts_mean(close, 20))",
            "correlation(close, volume, 15)",
            "ts_rank(close, 10) - 0.5",
            "-(close - ts_mean(close, 15)) / ts_std(close, 15)",
            "rank(volume) * rank(close/delay(close,1)-1)",
            "correlation(close/delay(close,1)-1, volume, 20)"
        ]
        results = automator.automated_alpha_pipeline(custom_expressions=custom_expressions)
        if results:
            summary = automator.analyze_alpha_performance(results)
            automator.save_results_to_csv(results)
            top = automator.filter_top_performers(results)

    elif choice == "7":
        print("\n=== Parameter Sweep Example ===")
        # Demonstrate parameter sweeping
        template = "correlation(close, volume, {window})"
        param_ranges = {"window": [10, 15, 20, 25, 30]}
        expressions = automator.generate_parameter_sweep_expressions(template, param_ranges)
        results = automator.automated_alpha_pipeline(custom_expressions=expressions)
        if results:
            summary = automator.analyze_alpha_performance(results)
            automator.save_results_to_csv(results)

    else:
        # Default: Basic pipeline
        print("\n=== Basic Automated Alpha Pipeline ===")
        results = automator.automated_alpha_pipeline()

        if results:
            print(f"\nSuccessfully processed {len(results)} alphas!")
            summary = automator.analyze_alpha_performance(results)

            print("\nPerformance Summary:")
            for i, alpha in enumerate(summary[:3], 1):  # Show top 3
                print(f"{i}. {alpha['expression']}")
                print(f"   Sharpe: {alpha['sharpe_ratio']}")
                print(f"   Fitness: {alpha['fitness']}")

            # Save results
            automator.save_results_to_csv(results)

    print("\n=== Automation Complete ===")

def demo_advanced_features():
    """Demonstrate advanced automation features"""
    automator = BrainAlphaAutomator()

    print("=== Advanced Features Demo ===")

    # 1. Generate momentum variations
    print("\n1. Momentum Variations:")
    momentum_exprs = automator.generate_momentum_variations(count=5)
    for expr in momentum_exprs:
        print(f"   {expr}")

    # 2. Generate parameter sweep
    print("\n2. Parameter Sweep:")
    template = "ts_mean(close, {short}) - ts_mean(close, {long})"
    param_ranges = {"short": [5, 10], "long": [20, 30]}
    sweep_exprs = automator.generate_parameter_sweep_expressions(template, param_ranges)
    for expr in sweep_exprs:
        print(f"   {expr}")

    # 3. Show all available expression types
    print("\n3. Available Expression Types:")
    expression_types = ["simple", "momentum", "mean_reversion", "volume_price", "rsi_based",
                       "bollinger", "macd", "volume_momentum", "price_volume_trend",
                       "volatility", "trend_strength"]
    for expr_type in expression_types:
        expr = automator.create_alpha_expression(expr_type)
        print(f"   {expr_type}: {expr}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_advanced_features()
    else:
        main()