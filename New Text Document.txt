import requests
import json
import time
from os.path import expanduser
from urllib.parse import urljoin

class BrainAlphaAutomator:
    def __init__(self, credentials_file='~/.brain_credentials'):
        self.session = requests.Session()
        self.base_url = 'https://api.worldquantbrain.com'
        self.load_credentials(credentials_file)
    
    def load_credentials(self, credentials_file):
        """Load credentials from JSON file"""
        try:
            with open(expanduser(credentials_file), 'r') as f:
                self.session.auth = tuple(json.load(f))
        except FileNotFoundError:
            print(f"Credentials file {credentials_file} not found. Please create it with format: ['email', 'password']")
    
    def authenticate(self):
        """Authenticate with the BRAIN API"""
        response = self.session.post(f'{self.base_url}/authentication')
        
        if response.status_code == 401:
            if response.headers.get("WWW-Authenticate") == "persona":
                # Handle biometric authentication
                auth_url = urljoin(response.url, response.headers["Location"])
                input(f"Complete biometrics authentication at: {auth_url}\nPress Enter to continue...")
                self.session.post(urljoin(response.url, response.headers["Location"]))
            else:
                raise Exception("Authentication failed: Incorrect email and password")
        elif response.status_code == 201:
            print("Authentication successful!")
            return response.json()
        else:
            raise Exception(f"Authentication failed with status code: {response.status_code}")
    
    def create_alpha_expression(self, expression_type="simple"):
        """Generate alpha expressions programmatically"""
        expressions = {
            "simple": "close",
            "momentum": "close / delay(close, 5) - 1",
            "mean_reversion": "-(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "volume_price": "correlation(close, volume, 10)",
            "rsi_based": "2 / (1 + exp(2 * ts_mean(close/delay(close,1)-1, 14) / ts_std(close/delay(close,1)-1, 14))) - 1"
        }
        return expressions.get(expression_type, expressions["simple"])
    
    def submit_alpha(self, alpha_expression, settings=None):
        """Submit an alpha for simulation"""
        if settings is None:
            # Default simulation settings
            settings = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'universe': 'TOP3000',
                'delay': 1,
                'decay': 15,
                'neutralization': 'SUBINDUSTRY',
                'truncation': 0.08,
                'pasteurization': 'ON',
                'testPeriod': 'P1Y6M',
                'unitHandling': 'VERIFY',
                'nanHandling': 'OFF',
                'language': 'FASTEXPR',
                'visualization': False,
            }
        
        simulation_data = {
            'type': 'REGULAR',
            'settings': settings,
            'regular': alpha_expression
        }
        
        print(f"Submitting alpha: {alpha_expression}")
        response = self.session.post(f'{self.base_url}/simulations', json=simulation_data)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"Alpha submitted successfully. Tracking at: {simulation_url}")
            return simulation_url
        else:
            raise Exception(f"Alpha submission failed: {response.status_code} - {response.text}")
    
    def wait_for_simulation(self, simulation_url):
        """Wait for simulation to complete and return alpha ID"""
        print("Waiting for simulation to complete...")
        
        while True:
            response = self.session.get(f'{self.base_url}{simulation_url}')
            data = response.json()
            
            if response.headers.get("Retry-After"):
                retry_after = int(response.headers["Retry-After"])
                print(f"Simulation in progress. Sleeping for {retry_after} seconds...")
                time.sleep(retry_after)
            else:
                if data.get('status') == 'COMPLETE':
                    alpha_id = data.get('alpha')
                    print(f"Simulation completed! Alpha ID: {alpha_id}")
                    return alpha_id
                elif data.get('status') in ['ERROR', 'FAIL', 'TIMEOUT']:
                    raise Exception(f"Simulation failed with status: {data.get('status')} - {data.get('message', 'No error message')}")
                else:
                    print(f"Simulation status: {data.get('status')}")
                    time.sleep(10)  # Wait 10 seconds before checking again
    
    def get_alpha_results(self, alpha_id):
        """Retrieve alpha simulation results"""
        print(f"Retrieving results for alpha: {alpha_id}")
        
        # Get alpha details
        alpha_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}')
        alpha_data = alpha_response.json()
        
        # Get available recordsets
        recordsets_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets')
        recordsets = recordsets_response.json()
        
        results = {
            'alpha_details': alpha_data,
            'available_recordsets': recordsets,
            'performance_data': {}
        }
        
        # Get key performance metrics
        key_metrics = ['pnl', 'sharpe', 'turnover', 'yearly-stats']
        
        for metric in key_metrics:
            try:
                # Wait for recordset to be ready
                while True:
                    metric_response = self.session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets/{metric}')
                    if metric_response.headers.get("Retry-After"):
                        retry_after = int(metric_response.headers["Retry-After"])
                        print(f"Waiting for {metric} data. Sleeping for {retry_after} seconds...")
                        time.sleep(retry_after)
                    else:
                        results['performance_data'][metric] = metric_response.json()
                        print(f"Retrieved {metric} data")
                        break
            except Exception as e:
                print(f"Could not retrieve {metric}: {e}")
        
        return results
    
    def submit_multiple_alphas(self, expressions_list, settings=None):
        """Submit multiple alphas simultaneously"""
        if len(expressions_list) < 2 or len(expressions_list) > 10:
            raise ValueError("Multiple simulations require 2-10 expressions")
        
        if settings is None:
            settings = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'universe': 'TOP3000',
                'delay': 1,
                'decay': 15,
                'neutralization': 'SUBINDUSTRY',
                'truncation': 0.08,
                'pasteurization': 'ON',
                'testPeriod': 'P1Y6M',
                'unitHandling': 'VERIFY',
                'nanHandling': 'OFF',
                'language': 'FASTEXPR',
                'visualization': False,
            }
        
        simulations = []
        for expr in expressions_list:
            simulations.append({
                'type': 'REGULAR',
                'settings': settings,
                'regular': expr
            })
        
        response = self.session.post(f'{self.base_url}/simulations', json=simulations)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"Multiple alphas submitted successfully. Tracking at: {simulation_url}")
            return simulation_url
        else:
            raise Exception(f"Multiple alpha submission failed: {response.status_code} - {response.text}")
    
    def automated_alpha_pipeline(self, expression_types=None, custom_expressions=None):
        """Complete automated pipeline: authenticate, create, submit, and retrieve results"""
        try:
            # Step 1: Authenticate
            self.authenticate()
            
            # Step 2: Create alpha expressions
            expressions = []
            if custom_expressions:
                expressions = custom_expressions
            else:
                if expression_types is None:
                    expression_types = ["simple", "momentum", "mean_reversion"]
                
                for expr_type in expression_types:
                    expressions.append(self.create_alpha_expression(expr_type))
            
            results = []
            
            # Step 3: Submit and process each alpha
            for i, expression in enumerate(expressions):
                print(f"\n--- Processing Alpha {i+1}/{len(expressions)} ---")
                
                # Submit alpha
                simulation_url = self.submit_alpha(expression)
                
                # Wait for completion
                alpha_id = self.wait_for_simulation(simulation_url)
                
                # Get results
                alpha_results = self.get_alpha_results(alpha_id)
                alpha_results['expression'] = expression
                results.append(alpha_results)
                
                print(f"Alpha {i+1} completed successfully!")
            
            return results
            
        except Exception as e:
            print(f"Pipeline failed: {e}")
            return None

# Example usage
def main():
    # Initialize the automator
    automator = BrainAlphaAutomator()
    
    # Example 1: Run automated pipeline with default expressions
    print("=== Running Automated Alpha Pipeline ===")
    results = automator.automated_alpha_pipeline()
    
    if results:
        print(f"\nSuccessfully processed {len(results)} alphas!")
        for i, result in enumerate(results):
            print(f"Alpha {i+1}: {result['expression']}")
            # Print some key metrics if available
            if 'pnl' in result['performance_data']:
                pnl_data = result['performance_data']['pnl']
                print(f"  Records available: {len(pnl_data.get('records', []))}")
    
    # Example 2: Submit custom expressions
    custom_expressions = [
        "rank(close / ts_mean(close, 20))",
        "correlation(close, volume, 15)",
        "ts_rank(close, 10) - 0.5"
    ]
    
    print("\n=== Running Custom Expressions ===")
    custom_results = automator.automated_alpha_pipeline(custom_expressions=custom_expressions)
    
    # Example 3: Submit multiple alphas simultaneously (requires MULTI_SIMULATION permission)
    try:
        print("\n=== Testing Multiple Alpha Submission ===")
        multi_url = automator.submit_multiple_alphas(custom_expressions[:3])
        multi_alpha_id = automator.wait_for_simulation(multi_url)
        print(f"Multiple alpha simulation completed: {multi_alpha_id}")
    except Exception as e:
        print(f"Multiple alpha submission not available: {e}")

if __name__ == "__main__":
    main()