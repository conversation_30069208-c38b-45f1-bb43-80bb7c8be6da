# 🚀 BRAIN Alpha Automation - Quick Start Guide

Your automated alpha generation system is **READY TO GO!** 

## ✅ System Status
- **Credentials**: ✅ Configured (`<EMAIL>`)
- **Dependencies**: ✅ Installed (requests, pandas)
- **API Integration**: ✅ Ready for WorldQuant BRAIN
- **Automation Scripts**: ✅ All systems operational

---

## 🎯 Start Automating NOW!

### Option 1: Interactive Mode (Recommended for first time)
```bash
python ALPHA.py
```
Choose from menu options:
- `1` - Basic pipeline (3 default expressions)
- `2` - Momentum strategy sweep (8 variations)
- `3` - Mean reversion sweep (8 variations)
- `4` - Correlation strategies (8 variations)
- `5` - Comprehensive multi-strategy (12 mixed)

### Option 2: Command Line (Quick automation)
```bash
# Test 10 momentum strategies
python run_alpha_automation.py momentum 10

# Test 15 comprehensive strategies
python run_alpha_automation.py comprehensive 15

# Test predefined custom expressions
python run_alpha_automation.py custom

# Test mean reversion strategies
python run_alpha_automation.py mean_reversion 8
```

### Option 3: Python Code (Advanced)
```python
from ALPHA import BrainAlphaAutomator

# Initialize
automator = BrainAlphaAutomator()

# Quick start - 3 default alphas
results = automator.automated_alpha_pipeline()

# Advanced - batch processing
results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=10)
```

---

## 🔥 What Happens When You Run It

1. **Authentication**: Automatic login to BRAIN API
2. **Expression Generation**: Creates alpha expressions based on strategy
3. **Submission**: Submits alphas for simulation
4. **Monitoring**: Waits for completion with progress updates
5. **Results**: Retrieves performance data (Sharpe, PnL, turnover)
6. **Analysis**: Ranks alphas and saves to CSV
7. **Summary**: Shows top performers

---

## 📊 Example Output

```
=== Processing Alpha 1/10 ===
Submitting alpha: close / delay(close, 15) - 1
Alpha submitted successfully. Tracking at: /simulations/12345
Waiting for simulation to complete...
Simulation completed! Alpha ID: alpha_67890
Retrieved pnl data
Retrieved sharpe data
Alpha 1 completed successfully!

Top 3 Performers:
1. close / delay(close, 15) - 1
   Sharpe: 0.8234
   Alpha ID: alpha_67890

Results saved to alpha_results_20240728_143022.csv
```

---

## 🎛️ Available Strategies

| Strategy | Description | Sample Expression |
|----------|-------------|-------------------|
| **momentum** | Price momentum with various lookbacks | `close / delay(close, 10) - 1` |
| **mean_reversion** | Mean reversion with different windows | `-(close - ts_mean(close, 20)) / ts_std(close, 20)` |
| **correlation** | Price-volume relationships | `correlation(close, volume, 15)` |
| **comprehensive** | Mix of all strategies | Multiple types combined |
| **custom** | Your own expressions | User-defined |

---

## 🔧 Customization

### Add Your Own Expressions
Edit `run_alpha_automation.py` and modify the `custom_expressions` list:
```python
custom_expressions = [
    "your_expression_1",
    "your_expression_2", 
    "your_expression_3"
]
```

### Parameter Sweeping
```python
template = "correlation(close, volume, {window})"
param_ranges = {"window": [10, 15, 20, 25, 30]}
expressions = automator.generate_parameter_sweep_expressions(template, param_ranges)
```

---

## 📈 Performance Analysis

The system automatically:
- ✅ Calculates Sharpe ratios
- ✅ Tracks total PnL
- ✅ Measures turnover
- ✅ Records fitness scores
- ✅ Exports to CSV
- ✅ Filters top performers

---

## 🚨 Important Notes

1. **Start Small**: Begin with 3-5 alphas to test the system
2. **Monitor Limits**: BRAIN API has rate limits - the system handles this automatically
3. **Biometric Auth**: If prompted, complete biometric authentication in browser
4. **Results**: All results are saved to timestamped CSV files
5. **Top Performers**: Look for Sharpe ratios > 0.3 as good performers

---

## 🎉 Ready to Launch!

**Your system is 100% ready for alpha automation!**

**Quick Start Command:**
```bash
python run_alpha_automation.py comprehensive 10
```

This will:
- Authenticate with your credentials
- Generate 10 diverse alpha strategies
- Submit them for simulation
- Monitor progress automatically
- Analyze and rank results
- Save everything to CSV

**Let the automation begin!** 🚀
