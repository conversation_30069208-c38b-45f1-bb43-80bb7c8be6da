#!/usr/bin/env python3
"""
Safe test script that respects the 25 login/day limit
This will establish a session once and reuse it for multiple operations
"""

from ALPHA import BrainAlphaAutomator
from session_manager import check_session_status

def safe_alpha_test():
    """Safely test alpha automation with session management"""
    print("🛡️ Safe Alpha Automation Test")
    print("=" * 50)
    
    # First, check session status
    print("📊 Checking current session status...")
    check_session_status()
    print()
    
    try:
        # Initialize automator (will load existing session if available)
        print("🚀 Initializing automator...")
        automator = BrainAlphaAutomator()
        
        # Ensure authentication (will reuse session or safely login)
        print("🔐 Ensuring authentication...")
        automator.ensure_authenticated()
        
        if not automator.authenticated:
            print("❌ Could not authenticate safely")
            return False
        
        print("✅ Authentication successful!")
        print()
        
        # Test expression generation (no API calls)
        print("🧪 Testing expression generation...")
        test_expressions = [
            automator.create_alpha_expression("momentum"),
            automator.create_alpha_expression("mean_reversion"),
            automator.create_alpha_expression("volume_price")
        ]
        
        print("Generated test expressions:")
        for i, expr in enumerate(test_expressions, 1):
            print(f"  {i}. {expr}")
        print()
        
        # Ask user if they want to proceed with actual submission
        print("🤔 Do you want to proceed with submitting ONE test alpha?")
        print("   This will use your authenticated session but won't create additional logins.")
        proceed = input("   Proceed? (yes/no): ").strip().lower()
        
        if proceed == 'yes':
            print("\n🚀 Submitting ONE test alpha...")
            
            # Submit just one alpha as a test
            test_expression = "close / delay(close, 10) - 1"
            print(f"📊 Testing expression: {test_expression}")
            
            # Submit alpha
            simulation_url = automator.submit_alpha(test_expression)
            print(f"✅ Alpha submitted successfully!")
            print(f"📍 Tracking URL: {simulation_url}")
            
            # Wait for completion
            print("⏳ Waiting for simulation to complete...")
            alpha_id = automator.wait_for_simulation(simulation_url)
            print(f"🎉 Simulation completed! Alpha ID: {alpha_id}")
            
            # Get basic results
            print("📈 Retrieving results...")
            results = automator.get_alpha_results(alpha_id)
            
            # Show summary
            print("\n📊 Results Summary:")
            print(f"Expression: {test_expression}")
            print(f"Alpha ID: {alpha_id}")
            
            if 'sharpe' in results['performance_data']:
                sharpe_data = results['performance_data']['sharpe']
                if 'records' in sharpe_data and sharpe_data['records']:
                    sharpe_ratio = sharpe_data['records'][-1].get('value', 'N/A')
                    print(f"Sharpe Ratio: {sharpe_ratio}")
            
            fitness = results['alpha_details'].get('fitness', 'N/A')
            print(f"Fitness: {fitness}")
            
            print("\n✅ Single alpha test completed successfully!")
            print("💾 Session has been saved for future use (valid for 23 hours)")
            
        else:
            print("✅ Test completed without API submission")
            print("💾 Session is ready for future use")
        
        # Final session status
        print("\n📊 Final session status:")
        check_session_status()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def demonstrate_session_reuse():
    """Demonstrate how session reuse works"""
    print("\n🔄 Demonstrating Session Reuse")
    print("=" * 40)
    
    print("Creating multiple automator instances...")
    
    # First instance
    print("1️⃣ First instance:")
    automator1 = BrainAlphaAutomator()
    print(f"   Authenticated: {automator1.authenticated}")
    
    # Second instance (should reuse session)
    print("2️⃣ Second instance:")
    automator2 = BrainAlphaAutomator()
    print(f"   Authenticated: {automator2.authenticated}")
    
    # Third instance (should reuse session)
    print("3️⃣ Third instance:")
    automator3 = BrainAlphaAutomator()
    print(f"   Authenticated: {automator3.authenticated}")
    
    print("\n✅ All instances should reuse the same session!")
    print("💡 This means you can run multiple scripts without additional logins")

if __name__ == "__main__":
    print("🛡️ BRAIN API Safe Testing with Login Limit Protection")
    print("=" * 70)
    print("This script respects the 25 login/day limit by:")
    print("• Reusing existing sessions when possible")
    print("• Tracking daily login count")
    print("• Stopping before hitting the limit")
    print("• Saving sessions for 23-hour reuse")
    print()
    
    success = safe_alpha_test()
    
    if success:
        demonstrate_session_reuse()
        print("\n🎉 Safe testing completed!")
        print("\n🚀 You can now run batch automation safely:")
        print("   python run_alpha_automation.py comprehensive 5")
        print("   (This will reuse your existing session)")
    else:
        print("\n🔧 Please check the error and try again")
        print("   Use: python session_manager.py to check status")
