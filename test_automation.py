#!/usr/bin/env python3
"""
Test script for BRAIN Alpha Automation System
This script demonstrates the key features without running full simulations
"""

from ALPHA import BrainAlphaAutomator
import json

def test_system_features():
    """Test all major features of the automation system"""
    print("🚀 BRAIN Alpha Automation System - Feature Test")
    print("=" * 60)
    
    # Initialize automator
    automator = BrainAlphaAutomator()
    print("✅ System initialized with credentials")
    
    # Test 1: Basic expression generation
    print("\n📊 Test 1: Basic Expression Generation")
    expression_types = ["momentum", "mean_reversion", "volume_price", "bollinger", "macd"]
    for expr_type in expression_types:
        expr = automator.create_alpha_expression(expr_type)
        print(f"  {expr_type:15}: {expr}")
    
    # Test 2: Momentum variations
    print("\n📈 Test 2: Momentum Strategy Variations")
    momentum_exprs = automator.generate_momentum_variations(count=5)
    for i, expr in enumerate(momentum_exprs, 1):
        print(f"  {i:2}. {expr}")
    
    # Test 3: Mean reversion variations
    print("\n📉 Test 3: Mean Reversion Strategy Variations")
    mean_rev_exprs = automator.generate_mean_reversion_variations(count=5)
    for i, expr in enumerate(mean_rev_exprs, 1):
        print(f"  {i:2}. {expr}")
    
    # Test 4: Correlation variations
    print("\n🔗 Test 4: Correlation Strategy Variations")
    corr_exprs = automator.generate_correlation_variations(count=5)
    for i, expr in enumerate(corr_exprs, 1):
        print(f"  {i:2}. {expr}")
    
    # Test 5: Parameter sweeping
    print("\n🔄 Test 5: Parameter Sweeping")
    template = "correlation(close, volume, {window})"
    param_ranges = {"window": [10, 15, 20, 25, 30]}
    sweep_exprs = automator.generate_parameter_sweep_expressions(template, param_ranges)
    for i, expr in enumerate(sweep_exprs, 1):
        print(f"  {i:2}. {expr}")
    
    # Test 6: Advanced parameter sweeping
    print("\n🎯 Test 6: Advanced Parameter Sweeping")
    template = "ts_mean(close, {short}) - ts_mean(close, {long})"
    param_ranges = {"short": [5, 10, 15], "long": [20, 30, 40]}
    advanced_sweep = automator.generate_parameter_sweep_expressions(template, param_ranges)
    print(f"  Generated {len(advanced_sweep)} expressions from parameter combinations:")
    for i, expr in enumerate(advanced_sweep[:6], 1):  # Show first 6
        print(f"  {i:2}. {expr}")
    if len(advanced_sweep) > 6:
        print(f"  ... and {len(advanced_sweep) - 6} more")
    
    print("\n✅ All feature tests completed successfully!")
    print("\n🎯 Ready for live alpha submission!")
    
    return True

def demonstrate_batch_strategies():
    """Demonstrate different batch strategy configurations"""
    print("\n🔥 Batch Strategy Demonstrations")
    print("=" * 60)
    
    automator = BrainAlphaAutomator()
    
    strategies = {
        "momentum_sweep": "Momentum strategies with varying lookback periods",
        "mean_reversion_sweep": "Mean reversion strategies with different windows", 
        "correlation_sweep": "Correlation-based strategies",
        "comprehensive": "Mixed strategy approach"
    }
    
    for strategy, description in strategies.items():
        print(f"\n📋 {strategy.upper().replace('_', ' ')}")
        print(f"   Description: {description}")
        
        # Show what expressions would be generated (without submitting)
        if strategy == "momentum_sweep":
            sample_exprs = automator.generate_momentum_variations(count=3)
        elif strategy == "mean_reversion_sweep":
            sample_exprs = automator.generate_mean_reversion_variations(count=3)
        elif strategy == "correlation_sweep":
            sample_exprs = automator.generate_correlation_variations(count=3)
        else:  # comprehensive
            momentum = automator.generate_momentum_variations(count=1)
            mean_rev = automator.generate_mean_reversion_variations(count=1)
            corr = automator.generate_correlation_variations(count=1)
            sample_exprs = momentum + mean_rev + corr
        
        print("   Sample expressions:")
        for i, expr in enumerate(sample_exprs, 1):
            print(f"     {i}. {expr}")

def show_usage_examples():
    """Show practical usage examples"""
    print("\n📚 Usage Examples")
    print("=" * 60)
    
    examples = [
        {
            "title": "Quick Start - Basic Pipeline",
            "code": """
# Run basic automation with default expressions
from ALPHA import BrainAlphaAutomator
automator = BrainAlphaAutomator()
results = automator.automated_alpha_pipeline()
"""
        },
        {
            "title": "Momentum Strategy Sweep",
            "code": """
# Test 10 momentum strategies
automator = BrainAlphaAutomator()
results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=10)
"""
        },
        {
            "title": "Custom Expression Testing",
            "code": """
# Test your own expressions
custom_expressions = [
    "rank(close / ts_mean(close, 20))",
    "correlation(close, volume, 15)",
    "-(close - ts_mean(close, 15)) / ts_std(close, 15)"
]
results = automator.automated_alpha_pipeline(custom_expressions=custom_expressions)
"""
        },
        {
            "title": "Parameter Sweeping",
            "code": """
# Systematically test parameter ranges
template = "correlation(close, volume, {window})"
param_ranges = {"window": [10, 15, 20, 25, 30]}
expressions = automator.generate_parameter_sweep_expressions(template, param_ranges)
results = automator.automated_alpha_pipeline(custom_expressions=expressions)
"""
        },
        {
            "title": "Command Line Usage",
            "code": """
# Quick command line execution
python run_alpha_automation.py momentum 8
python run_alpha_automation.py comprehensive 15
python run_alpha_automation.py custom
"""
        }
    ]
    
    for example in examples:
        print(f"\n🔹 {example['title']}")
        print(example['code'])

def main():
    """Main test function"""
    print("🧪 BRAIN Alpha Automation - System Test & Demo")
    print("=" * 70)
    
    try:
        # Run feature tests
        test_system_features()
        
        # Demonstrate batch strategies
        demonstrate_batch_strategies()
        
        # Show usage examples
        show_usage_examples()
        
        print("\n" + "=" * 70)
        print("🎉 SYSTEM READY FOR ALPHA AUTOMATION!")
        print("=" * 70)
        print("\n🚀 To start automating:")
        print("   • Interactive mode: python ALPHA.py")
        print("   • Quick start: python run_alpha_automation.py comprehensive 10")
        print("   • Custom expressions: Edit and run your own expressions")
        print("\n📊 Your credentials are configured and ready!")
        print("📧 Email: <EMAIL>")
        print("🔐 Password: [CONFIGURED]")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
