#!/usr/bin/env python3
"""
Test script to find valid BRAIN API operators
Tests simple expressions to see which operators work
"""

from ALPHA import BrainAlphaAutomator

def test_basic_operators():
    """Test basic operators to see what works in BRAIN API"""
    print("🧪 Testing BRAIN API Operators")
    print("=" * 50)
    
    # Simple test expressions to validate operators
    test_expressions = [
        ("close", "Basic close price"),
        ("rank(close)", "Rank function"),
        ("volume", "Basic volume"),
        ("rank(volume)", "Rank of volume"),
        ("close - lag(close, 1)", "Lag operator (1 day)"),
        ("close / lag(close, 1) - 1", "Simple return with lag"),
        ("ts_mean(close, 5)", "Time series mean"),
        ("ts_std(close, 5)", "Time series standard deviation"),
        ("correlation(close, volume, 10)", "Correlation function"),
        ("ts_rank(close, 10)", "Time series rank"),
        ("(close - ts_mean(close, 20)) / ts_std(close, 20)", "Z-score"),
        ("rank(close) - rank(volume)", "Rank difference"),
        ("close > lag(close, 1)", "Boolean comparison"),
        ("abs(close - lag(close, 1))", "Absolute value"),
        ("log(close)", "Logarithm"),
        ("sqrt(close)", "Square root"),
        ("max(close, lag(close, 1))", "Max function"),
        ("min(close, lag(close, 1))", "Min function")
    ]
    
    try:
        automator = BrainAlphaAutomator()
        automator.ensure_authenticated()
        
        if not automator.authenticated:
            print("❌ Authentication failed - cannot test operators")
            return
        
        print("✅ Authenticated - testing operators...")
        print("\nTesting expressions (will submit but not wait for completion):")
        print("-" * 60)
        
        successful_expressions = []
        failed_expressions = []
        
        for expression, description in test_expressions:
            try:
                print(f"Testing: {expression}")
                print(f"  Description: {description}")
                
                # Try to submit (this will validate the expression)
                simulation_url = automator.submit_alpha(expression)
                print(f"  ✅ SUCCESS - Expression accepted")
                successful_expressions.append((expression, description))
                
                # Don't wait for completion to save time
                print(f"  📍 Simulation URL: {simulation_url}")
                print()
                
            except Exception as e:
                print(f"  ❌ FAILED - {e}")
                failed_expressions.append((expression, description, str(e)))
                print()
        
        # Summary
        print("=" * 60)
        print("📊 RESULTS SUMMARY")
        print("=" * 60)
        
        print(f"\n✅ SUCCESSFUL EXPRESSIONS ({len(successful_expressions)}):")
        for expr, desc in successful_expressions:
            print(f"  • {expr}")
            print(f"    {desc}")
        
        print(f"\n❌ FAILED EXPRESSIONS ({len(failed_expressions)}):")
        for expr, desc, error in failed_expressions:
            print(f"  • {expr}")
            print(f"    {desc}")
            print(f"    Error: {error}")
        
        # Save successful expressions for future use
        if successful_expressions:
            print(f"\n💾 Saving {len(successful_expressions)} working expressions...")
            
            working_expressions = {
                "timestamp": str(datetime.now()),
                "expressions": [{"expression": expr, "description": desc} for expr, desc in successful_expressions]
            }
            
            with open('working_expressions.json', 'w') as f:
                json.dump(working_expressions, f, indent=2)
            
            print("✅ Saved to working_expressions.json")
        
        return successful_expressions, failed_expressions
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None, None

def quick_operator_test():
    """Quick test of just a few basic operators"""
    print("⚡ Quick Operator Test")
    print("=" * 30)
    
    # Just test the most basic ones
    basic_tests = [
        "close",
        "rank(close)", 
        "volume",
        "rank(volume)"
    ]
    
    try:
        automator = BrainAlphaAutomator()
        automator.ensure_authenticated()
        
        for expr in basic_tests:
            try:
                print(f"Testing: {expr}")
                simulation_url = automator.submit_alpha(expr)
                print(f"  ✅ Works!")
                # Don't wait for completion
                break  # Stop after first success
            except Exception as e:
                print(f"  ❌ Failed: {e}")
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")

if __name__ == "__main__":
    import sys
    import json
    from datetime import datetime
    
    print("🔬 BRAIN API Operator Testing")
    print("=" * 50)
    print("This script tests various operators to find what works")
    print("in the BRAIN API. It will submit expressions but not")
    print("wait for completion to save time.")
    print()
    
    choice = input("Run full test (f) or quick test (q)? [q]: ").strip().lower()
    
    if choice == 'f':
        successful, failed = test_basic_operators()
        
        if successful:
            print(f"\n🎉 Found {len(successful)} working expressions!")
            print("You can now use these in your alpha automation.")
    else:
        quick_operator_test()
    
    print("\n📚 Next steps:")
    print("  • Check working_expressions.json for valid operators")
    print("  • Update alpha expressions to use working operators")
    print("  • Run: python run_alpha_automation.py comprehensive 3")
