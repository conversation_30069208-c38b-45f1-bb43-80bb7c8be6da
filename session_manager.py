#!/usr/bin/env python3
"""
BRAIN API Session Manager
Helps manage authentication sessions to avoid hitting the 25 login/day limit
"""

import json
import os
import pickle
from datetime import datetime, timedelta
from ALPHA import BrainAlphaAutomator

def check_session_status():
    """Check current session and login count status"""
    print("🔍 BRAIN API Session Status")
    print("=" * 50)
    
    # Check session file
    session_file = '.brain_session.pkl'
    if os.path.exists(session_file):
        try:
            with open(session_file, 'rb') as f:
                session_data = pickle.load(f)
            
            age = datetime.now() - session_data['timestamp']
            hours_old = age.total_seconds() / 3600
            
            if hours_old < 23:
                print(f"✅ Valid session found (age: {hours_old:.1f} hours)")
                print(f"   Session expires in: {23 - hours_old:.1f} hours")
            else:
                print(f"⚠️ Session expired (age: {hours_old:.1f} hours)")
        except Exception as e:
            print(f"❌ Session file corrupted: {e}")
    else:
        print("❌ No session file found")
    
    # Check login count
    login_count_file = '.brain_login_count.json'
    today = datetime.now().strftime('%Y-%m-%d')
    
    if os.path.exists(login_count_file):
        try:
            with open(login_count_file, 'r') as f:
                login_data = json.load(f)
            
            if login_data.get('date') == today:
                count = login_data.get('count', 0)
                print(f"📊 Today's login count: {count}/25")
                
                if count >= 20:
                    print("🚨 CRITICAL: Near daily limit!")
                elif count >= 15:
                    print("⚠️ WARNING: Approaching daily limit")
                else:
                    print("✅ Safe login count")
            else:
                print("📊 No logins recorded for today")
        except Exception as e:
            print(f"❌ Could not read login count: {e}")
    else:
        print("📊 No login count file found")

def clear_session():
    """Clear existing session (force new login next time)"""
    session_file = '.brain_session.pkl'
    if os.path.exists(session_file):
        os.remove(session_file)
        print("🗑️ Session cleared - next run will require new login")
    else:
        print("❌ No session to clear")

def reset_login_count():
    """Reset login count (use carefully!)"""
    login_count_file = '.brain_login_count.json'
    if os.path.exists(login_count_file):
        response = input("⚠️ Are you sure you want to reset login count? (yes/no): ")
        if response.lower() == 'yes':
            os.remove(login_count_file)
            print("🔄 Login count reset")
        else:
            print("❌ Reset cancelled")
    else:
        print("❌ No login count to reset")

def test_session():
    """Test if current session works without using a new login"""
    print("🧪 Testing current session...")
    
    try:
        automator = BrainAlphaAutomator()
        
        if automator.authenticated:
            print("✅ Session is valid and ready to use!")
            return True
        else:
            print("❌ No valid session - new login required")
            return False
            
    except Exception as e:
        print(f"❌ Session test failed: {e}")
        return False

def safe_authenticate():
    """Safely authenticate with login limit checking"""
    print("🔐 Safe Authentication")
    print("=" * 30)
    
    try:
        automator = BrainAlphaAutomator()
        
        if automator.authenticated:
            print("✅ Already authenticated with existing session!")
            return True
        
        # Check if we can safely login
        login_count_file = '.brain_login_count.json'
        today = datetime.now().strftime('%Y-%m-%d')
        
        if os.path.exists(login_count_file):
            with open(login_count_file, 'r') as f:
                login_data = json.load(f)
            
            if login_data.get('date') == today:
                count = login_data.get('count', 0)
                if count >= 20:
                    print("🚨 CANNOT LOGIN: Daily limit reached!")
                    print("   Please wait until tomorrow or use existing session")
                    return False
        
        # Proceed with authentication
        result = automator.authenticate()
        if result:
            print("✅ Authentication successful and session saved!")
            return True
        
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False

def main():
    """Main session manager interface"""
    print("🔧 BRAIN API Session Manager")
    print("=" * 40)
    print("1. Check session status")
    print("2. Test current session")
    print("3. Safe authenticate")
    print("4. Clear session")
    print("5. Reset login count (careful!)")
    print("6. Exit")
    
    while True:
        choice = input("\nEnter choice (1-6): ").strip()
        
        if choice == "1":
            check_session_status()
        elif choice == "2":
            test_session()
        elif choice == "3":
            safe_authenticate()
        elif choice == "4":
            clear_session()
        elif choice == "5":
            reset_login_count()
        elif choice == "6":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
