#!/usr/bin/env python3
"""
Quick status check for BRAIN Alpha Automation System
"""

import os
import json
import pickle
from datetime import datetime, timedelta

def main():
    print("🔍 BRAIN Alpha Automation - System Status")
    print("=" * 60)
    
    # Check credentials
    if os.path.exists('.brain_credentials'):
        print("✅ Credentials: Configured")
        try:
            with open('.brain_credentials', 'r') as f:
                creds = json.load(f)
                print(f"   Email: {creds[0]}")
        except:
            print("   ⚠️ Credentials file may be corrupted")
    else:
        print("❌ Credentials: Not found")
    
    # Check session
    session_file = '.brain_session.pkl'
    if os.path.exists(session_file):
        try:
            with open(session_file, 'rb') as f:
                session_data = pickle.load(f)
            
            age = datetime.now() - session_data['timestamp']
            hours_old = age.total_seconds() / 3600
            
            if hours_old < 23:
                print(f"✅ Session: Valid (age: {hours_old:.1f} hours)")
                print(f"   Expires in: {23 - hours_old:.1f} hours")
            else:
                print(f"⚠️ Session: Expired (age: {hours_old:.1f} hours)")
        except Exception as e:
            print(f"❌ Session: Corrupted ({e})")
    else:
        print("❌ Session: None found")
    
    # Check login count
    login_count_file = '.brain_login_count.json'
    today = datetime.now().strftime('%Y-%m-%d')
    
    if os.path.exists(login_count_file):
        try:
            with open(login_count_file, 'r') as f:
                login_data = json.load(f)
            
            if login_data.get('date') == today:
                count = login_data.get('count', 0)
                print(f"📊 Today's logins: {count}/25")
                
                if count >= 20:
                    print("   🚨 CRITICAL: Near daily limit!")
                elif count >= 15:
                    print("   ⚠️ WARNING: Approaching daily limit")
                else:
                    print("   ✅ Safe login count")
            else:
                print("📊 Today's logins: 0/25")
        except Exception as e:
            print(f"❌ Login count: Error reading ({e})")
    else:
        print("📊 Today's logins: 0/25 (no file)")
    
    print("\n" + "=" * 60)
    
    # Recommendations
    session_valid = False
    if os.path.exists(session_file):
        try:
            with open(session_file, 'rb') as f:
                session_data = pickle.load(f)
            age = datetime.now() - session_data['timestamp']
            session_valid = age.total_seconds() / 3600 < 23
        except:
            pass
    
    if session_valid:
        print("🎉 READY TO RUN!")
        print("Your session is valid. You can run automation without new login:")
        print("   python run_alpha_automation.py comprehensive 5")
        print("   python test_single_alpha.py")
    else:
        print("🔐 AUTHENTICATION NEEDED")
        print("You need to authenticate first (will count as 1 login):")
        print("   python safe_test.py")
        print("   python session_manager.py  # Choose option 3")
    
    print("\n📚 Available commands:")
    print("   python session_manager.py     # Manage sessions")
    print("   python safe_test.py          # Safe testing")
    print("   python status_check.py       # This status check")

if __name__ == "__main__":
    main()
